# 📊 AI Festival 투자지원 플랫폼 - 파일 구조 분석

## 🎯 핵심 기능 개요

이 프로젝트는 **4가지 핵심 기능**을 제공하는 AI 기반 투자 분석 플랫폼입니다:

1. **🔍 1차 RAG 기반 산업 매칭** - 사용자 아이디어를 KOSPI 산업군과 매칭
2. **📰 2차 뉴스 기반 전략 도출** - 실시간 뉴스 분석으로 투자 전략 생성
3. **📈 포트폴리오 백테스팅** - AI 추천 포트폴리오의 과거 성과 분석
4. **⚡ SpeedTraffic™ 개별 기업 분석** - 4중 신호등 시스템으로 기업 분석

---

## 📁 src/components (React 컴포넌트)

### 🔥 **핵심 기능 직접 구현 파일들**

#### `AIChat.tsx` ⭐⭐⭐⭐⭐
- **쉬운 설명**: 사용자와 AI가 대화하는 채팅창
- **정확한 설명**: AI 채팅 인터페이스의 메인 컴포넌트로, 사용자 입력 처리, 메시지 히스토리 관리, 실시간 추론 과정 표시를 담당
- **핵심 기능 연결**: 
  - 🔍 **1차 RAG 매칭**: 사용자 입력을 AI 서비스로 전달
  - 📰 **2차 뉴스 분석**: 상세 분석 요청 및 진행 상황 표시
  - 📈 **포트폴리오 저장**: AI 추천 결과를 localStorage에 자동 저장

#### `SpeedTraffic.tsx` ⭐⭐⭐⭐⭐
- **쉬운 설명**: 개별 주식의 투자 신호등을 분석하는 백그라운드 작업자
- **정확한 설명**: SpeedTraffic™ 분석 API 호출 및 결과 처리를 담당하는 백그라운드 컴포넌트
- **핵심 기능 연결**: 
  - ⚡ **SpeedTraffic™**: 4중 분석(기술적/산업/시장/리스크) 실행의 핵심

#### `SpeedTrafficLights.tsx` ⭐⭐⭐⭐⭐
- **쉬운 설명**: 빨강/노랑/초록 신호등으로 투자 판단을 보여주는 화면
- **정확한 설명**: 4개 분석 영역(기술적/산업/시장/리스크)의 신호등 UI 및 종합 투자 신호 계산
- **핵심 기능 연결**: 
  - ⚡ **SpeedTraffic™**: 분석 결과의 직관적 시각화

#### `FinancialChart.tsx` ⭐⭐⭐⭐
- **쉬운 설명**: 주식 가격 차트를 그려주는 화면
- **정확한 설명**: lightweight-charts 라이브러리를 사용한 실시간 주가 차트 컴포넌트
- **핵심 기능 연결**: 
  - ⚡ **SpeedTraffic™**: 분석 대상 기업의 차트 표시

### 🎨 **UI/UX 지원 파일들**

#### `LandingPageNew.tsx` ⭐⭐⭐
- **쉬운 설명**: 웹사이트 첫 화면 (소개 페이지)
- **정확한 설명**: 4가지 핵심 기능을 소개하는 랜딩 페이지, 실제 UI 데모 포함

#### `RealTimeThinkingBox.tsx` ⭐⭐⭐
- **쉬운 설명**: AI가 생각하는 과정을 실시간으로 보여주는 말풍선
- **정확한 설명**: AI 분석 진행 상황을 실시간으로 표시하는 컴포넌트
- **핵심 기능 연결**: 
  - 📰 **2차 뉴스 분석**: 뉴스 검색/요약/전략 생성 과정 표시

#### `MarketStatus.tsx` ⭐⭐
- **쉬운 설명**: 코스피, 원달러 환율 등 시장 현황 표시
- **정확한 설명**: 실시간 시장 데이터(KOSPI, KRW/USD, VIX, 미국 10년 국채) 표시

#### `RealTimeAnalysis.tsx` ⭐⭐
- **쉬운 설명**: 실시간 시장 분석 정보 표시
- **정확한 설명**: 시장 지표들의 실시간 분석 결과 표시 (MarketStatus와 유사하지만 더 분석적)

#### `ReportModal.tsx` ⭐⭐
- **쉬운 설명**: SpeedTraffic 분석 결과를 팝업창으로 보여주는 화면
- **정확한 설명**: SpeedTraffic™ 분석 보고서를 모달 형태로 표시하는 컴포넌트

---

## 📊 src/data (데이터 파일)

### 🔥 **핵심 기능 직접 구현 파일들**

#### `KOSPI_companies.json` ⭐⭐⭐⭐⭐
- **쉬운 설명**: 코스피에 상장된 모든 회사 정보가 담긴 데이터베이스
- **정확한 설명**: 4,454개 KOSPI 상장 기업의 티커, 회사명, 산업분류, 주요사업 정보
- **핵심 기능 연결**: 
  - 🔍 **1차 RAG 매칭**: 기업 임베딩 생성의 기초 데이터
  - 📰 **2차 뉴스 분석**: 추천 기업 정보 제공

#### `kospi_enriched_final.ts` ⭐⭐⭐⭐⭐
- **쉬운 설명**: 회사 정보를 빠르게 찾을 수 있도록 정리한 데이터
- **정확한 설명**: 티커를 키로 하는 기업 정보 객체, 빠른 조회를 위한 최적화된 구조
- **핵심 기능 연결**: 
  - 🔍 **1차 RAG 매칭**: 기업명 조회
  - ⚡ **SpeedTraffic™**: 분석 대상 기업 정보 조회

#### `kospi_industry_vectors.ts` ⭐⭐⭐⭐⭐
- **쉬운 설명**: 각 산업별 특징을 나타내는 키워드 모음
- **정확한 설명**: 396개 산업 분류별 키워드 벡터, RAG 기반 산업 매칭의 핵심 데이터
- **핵심 기능 연결**: 
  - 🔍 **1차 RAG 매칭**: 사용자 입력과 산업 매칭의 핵심 데이터

#### `KOSPI_industry_mapping.json` ⭐⭐⭐⭐
- **쉬운 설명**: 산업 분류와 관련 키워드를 연결한 매핑 테이블
- **정확한 설명**: 산업별 키워드 매핑 정보 (kospi_industry_vectors.ts와 유사하지만 JSON 형태)

---

## 🛠️ src/lib (핵심 라이브러리)

### 🔥 **핵심 기능 직접 구현 파일들**

#### `clova-embedding.ts` ⭐⭐⭐⭐⭐
- **쉬운 설명**: 텍스트를 숫자 벡터로 변환하는 AI 도구
- **정확한 설명**: Clova Studio의 bge-m3 모델을 사용한 임베딩 생성 유틸리티
- **핵심 기능 연결**: 
  - 🔍 **1차 RAG 매칭**: 사용자 입력을 벡터로 변환하여 산업 매칭

#### `embeddings.ts` ⭐⭐⭐⭐⭐
- **쉬운 설명**: 미리 계산된 벡터 데이터를 빠르게 불러오는 도구
- **정확한 설명**: 임베딩 캐시 파일 로드 및 관리, 코사인 유사도 계산 함수 제공
- **핵심 기능 연결**: 
  - 🔍 **1차 RAG 매칭**: 캐시된 기업/산업 임베딩 로드 및 유사도 계산

### 🔧 **지원 기능 파일들**

#### `server-session.ts` ⭐⭐
- **쉬운 설명**: 서버 재시작을 감지하고 사용자 데이터를 관리하는 도구
- **정확한 설명**: 서버 세션 관리, 재시작 감지, 포트폴리오 데이터 보존 로직

#### `utils.ts` ⭐
- **쉬운 설명**: 여러 곳에서 쓰이는 공통 도구 모음
- **정확한 설명**: Tailwind CSS 클래스 병합을 위한 유틸리티 함수

---

## 🤖 src/lib/ai-chat (AI 채팅 시스템)

### 🔥 **핵심 기능 직접 구현 파일들**

#### `ai-service.ts` ⭐⭐⭐⭐⭐
- **쉬운 설명**: AI와 대화하고 투자 분석을 요청하는 핵심 엔진
- **정확한 설명**: OpenAI 호환 클라이언트 초기화, 의도 분류, GPT 기반 응답 생성
- **핵심 기능 연결**: 
  - 🔍 **1차 RAG 매칭**: 사용자 의도 분류 및 초기 응답 생성
  - 📰 **2차 뉴스 분석**: 상세 투자 분석 요청 처리

#### `rag-service.ts` ⭐⭐⭐⭐⭐
- **쉬운 설명**: 사용자 말을 이해해서 적합한 산업을 찾아주는 검색 엔진
- **정확한 설명**: RAG 기반 의도 분류, 임베딩 유사도 계산, 산업 매칭 로직
- **핵심 기능 연결**: 
  - 🔍 **1차 RAG 매칭**: 핵심 구현체, 사용자 입력을 산업과 매칭

#### `news-service.ts` ⭐⭐⭐⭐⭐
- **쉬운 설명**: 네이버에서 관련 뉴스를 찾아오는 뉴스 수집기
- **정확한 설명**: Naver News API를 활용한 뉴스 검색, 필터링, 데이터 정제
- **핵심 기능 연결**: 
  - 📰 **2차 뉴스 분석**: 투자 전략 생성을 위한 뉴스 데이터 수집

#### `function-calling-tools.ts` ⭐⭐⭐⭐⭐
- **쉬운 설명**: AI가 뉴스 검색, 요약 등의 작업을 수행할 수 있게 해주는 도구상자
- **정확한 설명**: HCX-005 모델용 Function Calling 도구 정의 및 실행기
- **핵심 기능 연결**: 
  - 📰 **2차 뉴스 분석**: AI가 뉴스 검색/요약/분석을 자동으로 수행

#### `summary-service.ts` ⭐⭐⭐⭐
- **쉬운 설명**: 긴 뉴스 기사를 짧게 요약해주는 요약기
- **정확한 설명**: Naver 요약 API를 활용한 뉴스 요약 서비스
- **핵심 기능 연결**: 
  - 📰 **2차 뉴스 분석**: 수집된 뉴스의 핵심 내용 추출

#### `pipeline-handlers.ts` ⭐⭐⭐⭐
- **쉬운 설명**: 사용자 요청을 단계별로 처리하는 작업 관리자
- **정확한 설명**: 의도별 파이프라인 핸들러, 대화형/투자 쿼리 처리 로직
- **핵심 기능 연결**: 
  - 🔍 **1차 RAG 매칭**: 의도 분류 결과에 따른 처리 분기
  - 📰 **2차 뉴스 분석**: 투자 쿼리 처리 파이프라인

### 🔧 **지원 기능 파일들**

#### `config.ts` ⭐⭐⭐
- **쉬운 설명**: AI 시스템의 모든 설정값들이 모여있는 설정 파일
- **정확한 설명**: RAG 임계값, 패턴 매칭, API 설정, 시스템 프롬프트 등 통합 설정

#### `types.ts` ⭐⭐⭐
- **쉬운 설명**: AI 시스템에서 사용하는 데이터 형태를 정의한 설계도
- **정확한 설명**: TypeScript 인터페이스 및 타입 정의

#### `request-handler.ts` ⭐⭐⭐
- **쉬운 설명**: 사용자 요청을 받아서 적절히 처리하는 요청 처리기
- **정확한 설명**: AI 채팅 요청의 메인 핸들러, 세션 관리 및 응답 생성

#### `session-manager.ts` ⭐⭐
- **쉬운 설명**: 사용자별 대화 기록을 관리하는 메모리 관리자
- **정확한 설명**: 세션 기반 대화 히스토리 관리, 자동 정리 기능

#### `company-utils.ts` ⭐⭐
- **쉬운 설명**: 회사 정보를 찾고 정리하는 도구 모음
- **정확한 설명**: 기업 데이터 조회, 패턴 매칭, 포맷팅 유틸리티

#### `speedtraffic-prompts.ts` ⭐⭐
- **쉬운 설명**: SpeedTraffic 분석용 AI 명령어 모음
- **정확한 설명**: SpeedTraffic™ 분석을 위한 시스템 프롬프트 정의

#### `index.ts` ⭐
- **쉬운 설명**: ai-chat 모듈의 진입점
- **정확한 설명**: 모듈 내 주요 함수들의 export 관리

---

## 🎯 핵심 기능별 파일 연관도

### 🔍 **1차 RAG 기반 산업 매칭**
```
사용자 입력 → AIChat.tsx → ai-service.ts → rag-service.ts 
→ embeddings.ts + clova-embedding.ts → kospi_industry_vectors.ts
```

### 📰 **2차 뉴스 기반 전략 도출**
```
산업 매칭 결과 → function-calling-tools.ts → news-service.ts 
→ summary-service.ts → ai-service.ts → 최종 투자 전략
```

### 📈 **포트폴리오 백테스팅**
```
AI 추천 결과 → AIChat.tsx (localStorage 저장) → 포트폴리오 페이지
```

### ⚡ **SpeedTraffic™ 개별 기업 분석**
```
기업 선택 → SpeedTraffic.tsx → API 호출 → SpeedTrafficLights.tsx
→ FinancialChart.tsx + ReportModal.tsx
```

---

## 📈 파일 중요도 등급

- ⭐⭐⭐⭐⭐ **핵심 구현체** (9개): 4가지 주요 기능을 직접 구현
- ⭐⭐⭐⭐ **주요 지원** (4개): 핵심 기능의 중요한 지원 역할
- ⭐⭐⭐ **UI/설정** (6개): 사용자 경험 및 시스템 설정
- ⭐⭐ **보조 기능** (6개): 부가적인 지원 기능
- ⭐ **유틸리티** (2개): 공통 도구 및 진입점

**총 27개 파일**이 유기적으로 연결되어 **AI 기반 투자 분석 플랫폼**을 구성합니다.
